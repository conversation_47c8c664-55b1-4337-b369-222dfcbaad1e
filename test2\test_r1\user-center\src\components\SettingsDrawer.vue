<template>
  <!-- 抽屉遮罩 -->
  <div 
    class="drawer-overlay" 
    v-if="visible" 
    @click="$emit('close')"
    :class="{ 'drawer-overlay-visible': visible }"
  ></div>
  
  <!-- 抽屉内容 -->
  <div 
    class="settings-drawer" 
    :class="{ 'drawer-visible': visible }"
  >
    <div class="drawer-header">
      <h3>设置</h3>
      <button class="close-btn" @click="$emit('close')">×</button>
    </div>
    
    <div class="drawer-content">
      <!-- 用户信息区域 -->
      <div class="user-section">
        <div class="user-info">
          <div class="user-avatar">
            <img src="/avatar-placeholder.svg" alt="用户头像" />
          </div>
          <div class="user-details">
            <div class="user-name">{{ userStore.nickname }}</div>
            <div class="user-phone">{{ userStore.phone }}</div>
          </div>
          <div class="user-arrow">
            <i>›</i>
          </div>
        </div>
      </div>
      
      <!-- 通用设置 -->
      <div class="settings-section">
        <div class="section-title">通用</div>
        
        <div class="setting-item" @click="showThemeSettings">
          <div class="setting-icon">
            <i>☀️</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">界面主题</div>
            <div class="setting-value">{{ currentThemeName }}</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-icon">
            <i>🌐</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">Language</div>
            <div class="setting-value">中文</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
      </div>
      
      <!-- 会话设置 -->
      <div class="settings-section">
        <div class="section-title">会话</div>
        
        <div class="setting-item">
          <div class="setting-icon">
            <i>📦</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">常用语</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
        
        <div class="setting-item">
          <div class="setting-icon">
            <i>📖</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">自动展开参考网页</div>
          </div>
          <div class="setting-toggle">
            <label class="toggle-switch">
              <input type="checkbox" v-model="autoExpandRef" />
              <span class="toggle-slider"></span>
            </label>
          </div>
        </div>
      </div>
      
      <!-- 关于我们 -->
      <div class="settings-section">
        <div class="section-title">关于我们</div>
        
        <div class="setting-item" @click="$emit('show-feedback')">
          <div class="setting-icon">
            <i>💬</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">用户反馈</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
        
        <div class="setting-item" @click="$emit('show-about')">
          <div class="setting-icon">
            <i>📄</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">用户协议</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
        
        <div class="setting-item" @click="$emit('show-about')">
          <div class="setting-icon">
            <i>🔒</i>
          </div>
          <div class="setting-content">
            <div class="setting-label">隐私协议</div>
          </div>
          <div class="setting-arrow">
            <i>›</i>
          </div>
        </div>
      </div>
      
      <!-- 退出登录 -->
      <div class="logout-section">
        <button class="logout-btn" @click="handleLogout">
          <i>🚪</i>
          退出登录
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { useUserStore } from '@/stores/user'
import { useThemeStore } from '@/stores/theme'
import { useRouter } from 'vue-router'

defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
  'show-feedback': []
  'show-about': []
}>()

const userStore = useUserStore()
const themeStore = useThemeStore()
const router = useRouter()

const autoExpandRef = ref(true)

// 当前主题名称
const currentThemeName = computed(() => {
  if (themeStore.useCustomTheme) {
    return '自定义'
  }
  const themeMap: Record<string, string> = {
    orange: '月之亮面',
    blue: '蓝色主题',
    green: '绿色主题',
    purple: '紫色主题',
    red: '红色主题',
    teal: '青色主题'
  }
  return themeMap[themeStore.currentTheme] || '月之亮面'
})

// 显示主题设置
const showThemeSettings = () => {
  emit('close')
  router.push('/theme-settings')
}

// 退出登录
const handleLogout = () => {
  if (confirm('确定要退出登录吗？')) {
    // 这里添加退出登录逻辑
    console.log('退出登录')
    emit('close')
  }
}
</script>

<style scoped>
.drawer-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1500;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.drawer-overlay-visible {
  opacity: 1;
  visibility: visible;
}

.settings-drawer {
  position: fixed;
  top: 0;
  right: 0;
  width: 375px;
  height: 100vh;
  background: var(--color-surface);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
  z-index: 1600;
  transform: translateX(100%);
  transition: transform 0.3s ease;
  overflow-y: auto;
}

.drawer-visible {
  transform: translateX(0);
}

.drawer-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
  background: var(--color-surface);
  position: sticky;
  top: 0;
  z-index: 10;
}

.drawer-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.drawer-content {
  padding: 0 0 24px 0;
}

.user-section {
  padding: 20px 24px;
  border-bottom: 1px solid var(--color-border);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  cursor: pointer;
  padding: 8px;
  border-radius: 8px;
  transition: background-color 0.2s ease;
}

.user-info:hover {
  background-color: var(--theme-primary-light);
}

.user-avatar {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--color-border);
  display: flex;
  align-items: center;
  justify-content: center;
}

.user-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin-bottom: 2px;
}

.user-phone {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.user-arrow {
  color: var(--color-text-muted);
  font-size: 18px;
}

.settings-section {
  margin-bottom: 32px;
}

.section-title {
  padding: 16px 24px 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-muted);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.setting-item {
  display: flex;
  align-items: center;
  padding: 16px 24px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.setting-item:hover {
  background-color: var(--theme-primary-light);
}

.setting-icon {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  margin-right: 12px;
}

.setting-content {
  flex: 1;
}

.setting-label {
  font-size: 16px;
  color: var(--color-text-primary);
  margin-bottom: 2px;
}

.setting-value {
  font-size: 14px;
  color: var(--color-text-secondary);
}

.setting-arrow {
  color: var(--color-text-muted);
  font-size: 18px;
}

.setting-toggle {
  margin-left: auto;
}

.toggle-switch {
  position: relative;
  display: inline-block;
  width: 44px;
  height: 24px;
}

.toggle-switch input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #ccc;
  transition: 0.3s;
  border-radius: 24px;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  transition: 0.3s;
  border-radius: 50%;
}

input:checked + .toggle-slider {
  background-color: var(--theme-primary);
}

input:checked + .toggle-slider:before {
  transform: translateX(20px);
}

.logout-section {
  padding: 0 24px;
  margin-top: 32px;
}

.logout-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  padding: 12px;
  background: transparent;
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text-secondary);
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.logout-btn:hover {
  border-color: var(--color-danger);
  color: var(--color-danger);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .settings-drawer {
    width: 100%;
  }
}
</style>
