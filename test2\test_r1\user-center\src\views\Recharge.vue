<script setup lang="ts">
import { ref } from 'vue'
import { useUserStore } from '@/stores/user'

const userStore = useUserStore()
const amount = ref(10)
const paymentMethod = ref('wechat')
const customAmount = ref('')
const showCustomInput = ref(false)

const rechargeOptions = [
  { label: '10元', value: 10 },
  { label: '20元', value: 20 },
  { label: '50元', value: 50 },
  { label: '100元', value: 100 },
  { label: '自定义', value: 'custom' }
]

const handleAmountChange = (value: number | string) => {
  if (value === 'custom') {
    showCustomInput.value = true
    customAmount.value = ''
  } else {
    showCustomInput.value = false
    amount.value = value as number
  }
}

const handleRecharge = () => {
  let rechargeAmount = amount.value
  
  if (showCustomInput.value && customAmount.value) {
    const num = parseFloat(customAmount.value)
    if (!isNaN(num) && num > 0) {
      rechargeAmount = num
    } else {
      alert('请输入有效的充值金额')
      return
    }
  }
  
  userStore.updateBalance(rechargeAmount)
  alert(`充值成功！¥${rechargeAmount.toFixed(2)} 已添加到您的账户`)
}
</script>

<template>
  <div class="recharge">
    <!-- 页面标题 -->
    <div class="page-header">
      <h1 class="page-title">充值</h1>
    </div>

    <!-- 充值选项卡 -->
    <div class="recharge-tabs">
      <div class="tab-item active">在线充值</div>
      <div class="tab-item">对公转账</div>
    </div>

    <!-- 充值金额选择 -->
    <div class="amount-section">
      <h3 class="section-title">充值金额</h3>
      <div class="amount-grid">
        <div
          v-for="option in rechargeOptions.slice(0, -1)"
          :key="option.value"
          class="amount-card"
          :class="{ active: amount === option.value && !showCustomInput }"
          @click="handleAmountChange(option.value)"
        >
          {{ option.label }}
        </div>
        <div
          class="amount-card custom-card"
          :class="{ active: showCustomInput }"
          @click="handleAmountChange('custom')"
        >
          自定义
        </div>
        <div
          class="amount-card custom-card"
          :class="{ active: showCustomInput }"
          @click="handleAmountChange('custom')"
        >
          自有价格
        </div>
      </div>

      <!-- 自定义金额输入 -->
      <div v-if="showCustomInput" class="custom-input-section">
        <div class="custom-input-wrapper">
          <span class="currency-symbol">¥</span>
          <input
            v-model="customAmount"
            type="number"
            placeholder="请输入充值金额"
            class="custom-input"
          />
          <button class="clear-btn" @click="customAmount = ''" v-if="customAmount">×</button>
        </div>
      </div>
    </div>

    <!-- 优惠信息 -->
    <div class="promotion-info">
      <p class="promotion-text">
        【注意】充值不可退，且充值时间为 00:30-08:30 为维护时间段，API 调用可能会受到影响。DeepSeek-V3 首发期间享受
        50% DeepSeek 时长 2.5% 折扣，充值时间为 00:30-08:30 为维护时间段，API 调用可能会受到影响。
        <a href="#" class="promotion-link">【查看活动详情】</a>
      </p>
    </div>

    <!-- 支付方式 -->
    <div class="payment-section">
      <h3 class="section-title">支付方式</h3>
      <div class="payment-method-card">
        <div class="payment-option" :class="{ active: paymentMethod === 'alipay' }" @click="paymentMethod = 'alipay'">
          <div class="payment-icon">💳</div>
          <span>支付宝</span>
        </div>
      </div>

      <div class="payment-method-card wechat-card">
        <div class="payment-option" :class="{ active: paymentMethod === 'wechat' }" @click="paymentMethod = 'wechat'">
          <div class="payment-icon wechat-icon">💚</div>
          <span>微信支付</span>
        </div>
      </div>
    </div>

    <!-- 立即支付按钮 -->
    <div class="action-section">
      <button class="pay-button" @click="handleRecharge">
        立即支付
      </button>
    </div>

    <!-- 提示信息 -->
    <div class="tips-section">
      <h4>提示</h4>
      <p>1. 充值金额用于调用 API 服务，每次调用及 App 对话充值使用，不支持。</p>
      <p>2. 充值后可立即使用，无需等待审核。</p>
    </div>
  </div>
</template>

<style scoped>
.recharge {
  max-width: 800px;
  margin: 0 auto;
  padding: 0;
}

/* 页面标题 */
.page-header {
  margin-bottom: 24px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0;
}

/* 充值选项卡 */
.recharge-tabs {
  display: flex;
  gap: 0;
  margin-bottom: 24px;
  border-bottom: 1px solid #e5e5e5;
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
}

.tab-item {
  padding: 12px 20px;
  font-size: 16px;
  font-weight: 500;
  color: #666666;
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  white-space: nowrap;
  flex-shrink: 0;
}

.tab-item.active {
  color: var(--theme-primary);
  border-bottom-color: var(--theme-primary);
}

/* 充值金额选择 */
.amount-section {
  margin-bottom: 24px;
}

.section-title {
  font-size: 18px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 16px 0;
}

.amount-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
  gap: 12px;
  margin-bottom: 16px;
}

.amount-card {
  padding: 14px 16px;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  text-align: center;
  font-size: 14px;
  font-weight: 500;
  color: #1a1a1a;
  cursor: pointer;
  transition: all 0.2s ease;
  background: #ffffff;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.amount-card:hover {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-light);
}

.amount-card.active {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-light);
  color: var(--theme-primary);
}

.custom-card {
  background-color: #f8f9fa;
}

/* 自定义金额输入 */
.custom-input-section {
  margin-top: 16px;
}

.custom-input-wrapper {
  position: relative;
  display: inline-flex;
  align-items: center;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  background: #ffffff;
  padding: 0 16px;
  min-width: 300px;
}

.currency-symbol {
  color: #666666;
  font-size: 16px;
  margin-right: 8px;
}

.custom-input {
  border: none;
  outline: none;
  font-size: 16px;
  padding: 12px 0;
  flex: 1;
  background: transparent;
}

.clear-btn {
  background: none;
  border: none;
  color: #999999;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  margin-left: 8px;
}

/* 优惠信息 */
.promotion-info {
  background: var(--theme-primary-light);
  border: 1px solid var(--theme-secondary);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 32px;
}

.promotion-text {
  font-size: 14px;
  color: var(--theme-primary-dark);
  margin: 0;
  line-height: 1.5;
}

.promotion-link {
  color: var(--theme-primary);
  text-decoration: none;
}

.promotion-link:hover {
  text-decoration: underline;
}

/* 支付方式 */
.payment-section {
  margin-bottom: 32px;
}

.payment-method-card {
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  margin-bottom: 12px;
  overflow: hidden;
  background: #ffffff;
}

.payment-option {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.payment-option:hover {
  background-color: #f8f9fa;
}

.payment-option.active {
  background-color: var(--theme-primary-light);
  border-color: var(--theme-primary);
}

.payment-icon {
  font-size: 20px;
  margin-right: 12px;
  width: 24px;
  text-align: center;
}

.wechat-icon {
  color: #07c160;
}

/* 立即支付按钮 */
.action-section {
  margin-bottom: 32px;
}

.pay-button {
  width: 100%;
  background: #1a1a1a;
  color: white;
  border: none;
  border-radius: 8px;
  padding: 16px 32px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.pay-button:hover {
  background: #333333;
}

/* 提示信息 */
.tips-section {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.tips-section h4 {
  font-size: 16px;
  font-weight: 600;
  color: #1a1a1a;
  margin: 0 0 12px 0;
}

.tips-section p {
  font-size: 14px;
  color: #666666;
  margin: 8px 0;
  line-height: 1.5;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .recharge {
    padding: 0;
  }

  .page-title {
    font-size: 24px;
  }

  .recharge-tabs {
    margin-bottom: 20px;
  }

  .tab-item {
    padding: 10px 16px;
    font-size: 14px;
  }

  .amount-section,
  .payment-section,
  .action-section {
    margin-bottom: 20px;
  }

  .section-title {
    font-size: 16px;
  }

  .amount-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 8px;
  }

  .amount-card {
    padding: 12px 8px;
    font-size: 13px;
    min-height: 44px;
  }

  .custom-input-wrapper {
    min-width: 100%;
    max-width: 100%;
  }

  .custom-input {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .promotion-info {
    padding: 12px;
    margin-bottom: 20px;
  }

  .promotion-text {
    font-size: 13px;
  }

  .payment-method-card {
    margin-bottom: 8px;
  }

  .payment-option {
    padding: 14px 16px;
  }

  .pay-button {
    padding: 14px 24px;
    font-size: 16px;
  }

  .tips-section {
    padding: 16px;
  }

  .tips-section h4 {
    font-size: 14px;
  }

  .tips-section p {
    font-size: 13px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .page-title {
    font-size: 26px;
  }

  .amount-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 10px;
  }

  .amount-card {
    padding: 14px 12px;
    font-size: 15px;
  }

  .custom-input-wrapper {
    min-width: 280px;
  }
}

@media (min-width: 1024px) {
  .amount-grid {
    grid-template-columns: repeat(6, 1fr);
  }

  .amount-card {
    padding: 16px 20px;
    font-size: 16px;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .amount-card,
  .payment-option,
  .pay-button {
    min-height: 44px;
  }

  .tab-item {
    min-height: 44px;
    display: flex;
    align-items: center;
  }
}
</style>
