<script setup lang="ts">
import { ref } from 'vue'

const activeTab = ref('all')
const billTypes = [
  { label: '全部账单', name: 'all' },
  { label: '购买账单', name: 'purchase' },
  { label: '消费账单', name: 'consumption' }
]

const bills = ref([
  { 
    id: '1', 
    type: 'purchase', 
    amount: 100.00, 
    time: '2023-10-10 14:30', 
    description: '账户充值',
    status: 'completed'
  },
  { 
    id: '2', 
    type: 'consumption', 
    amount: -15.80, 
    time: '2023-10-09 09:15', 
    description: 'API调用费用',
    status: 'completed'
  },
  { 
    id: '3', 
    type: 'purchase', 
    amount: 50.00, 
    time: '2023-10-05 16:45', 
    description: '账户充值',
    status: 'completed'
  },
  { 
    id: '4', 
    type: 'consumption', 
    amount: -8.20, 
    time: '2023-10-03 11:20', 
    description: 'API调用费用',
    status: 'completed'
  },
  { 
    id: '5', 
    type: 'consumption', 
    amount: -22.50, 
    time: '2023-10-01 18:05', 
    description: 'API调用费用',
    status: 'completed'
  }
])

const filteredBills = ref(bills.value)

const filterBills = () => {
  if (activeTab.value === 'all') {
    filteredBills.value = bills.value
  } else {
    filteredBills.value = bills.value.filter(bill => bill.type === activeTab.value)
  }
}
</script>

<template>
  <div class="bill-manage">
    <h2>账单管理</h2>
    
    <el-tabs v-model="activeTab" @tab-change="filterBills" class="bill-tabs">
      <el-tab-pane 
        v-for="type in billTypes" 
        :key="type.name" 
        :label="type.label" 
        :name="type.name"
      />
    </el-tabs>
    
    <div class="bill-list">
      <el-table :data="filteredBills" style="width: 100%" border>
        <el-table-column prop="time" label="时间" width="180" />
        <el-table-column prop="description" label="描述" />
        <el-table-column label="类型" width="100">
          <template #default="scope">
            <el-tag 
              :type="scope.row.type === 'purchase' ? 'success' : 'danger'"
            >
              {{ scope.row.type === 'purchase' ? '购买' : '消费' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="金额" width="120">
          <template #default="scope">
            <span :class="scope.row.type === 'purchase' ? 'positive' : 'negative'">
              {{ scope.row.type === 'purchase' ? '+' : '' }}{{ scope.row.amount.toFixed(2) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column prop="status" label="状态" width="100">
          <template #default="scope">
            <el-tag 
              type="success" 
              v-if="scope.row.status === 'completed'"
            >
              已完成
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </div>
  </div>
</template>

<style scoped>
.bill-manage {
  padding: 20px;
}

.bill-tabs {
  margin-top: 20px;
}

.bill-list {
  margin-top: 20px;
}

.positive {
  color: #67c23a;
  font-weight: bold;
}

.negative {
  color: #f56c6c;
  font-weight: bold;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .bill-manage {
    padding: 16px;
  }

  .header h2 {
    font-size: 20px;
  }

  .bill-tabs {
    margin-top: 16px;
  }

  .bill-list {
    margin-top: 16px;
  }

  /* 移动端表格优化 */
  .el-table {
    font-size: 12px;
  }

  .el-table .el-table__cell {
    padding: 6px 4px;
  }

  .el-table__header th {
    font-size: 12px;
  }

  .el-tag {
    font-size: 10px;
    padding: 0 4px;
  }

  .positive,
  .negative {
    font-size: 12px;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .bill-manage {
    padding: 20px;
  }

  .el-table {
    font-size: 14px;
  }
}

/* 表格水平滚动优化 */
@media (max-width: 1023px) {
  .bill-list {
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .el-table {
    min-width: 600px;
  }
}
</style>
