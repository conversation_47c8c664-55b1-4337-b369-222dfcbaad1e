<template>
  <div class="theme-selector">
    <div class="theme-selector-trigger" @click="showSelector = !showSelector">
      <div class="current-theme-indicator" :style="{ backgroundColor: themeStore.activeTheme.primary }"></div>
      <span>主题</span>
      <i class="arrow" :class="{ 'arrow-up': showSelector }">▼</i>
    </div>
    
    <div v-if="showSelector" class="theme-selector-panel">
      <div class="theme-section">
        <h4>预设主题</h4>
        <div class="theme-grid">
          <div 
            v-for="theme in availableThemes" 
            :key="theme.key"
            class="theme-option"
            :class="{ active: !themeStore.useCustomTheme && themeStore.currentTheme === theme.key }"
            @click="selectTheme(theme.key)"
          >
            <div class="theme-preview">
              <div class="color-dot primary" :style="{ backgroundColor: theme.primary }"></div>
              <div class="color-dot secondary" :style="{ backgroundColor: theme.secondary }"></div>
              <div class="color-dot accent" :style="{ backgroundColor: theme.accent }"></div>
            </div>
            <span class="theme-name">{{ theme.name }}</span>
          </div>
        </div>
      </div>
      
      <div class="theme-section">
        <h4>自定义主题</h4>
        <div class="custom-theme-editor">
          <div class="color-input-group">
            <label>主色调</label>
            <input 
              type="color" 
              v-model="customColors.primary" 
              @change="updateCustomTheme"
              class="color-input"
            />
          </div>
          <div class="color-input-group">
            <label>浅色调</label>
            <input 
              type="color" 
              v-model="customColors.primaryLight" 
              @change="updateCustomTheme"
              class="color-input"
            />
          </div>
          <div class="color-input-group">
            <label>深色调</label>
            <input 
              type="color" 
              v-model="customColors.primaryDark" 
              @change="updateCustomTheme"
              class="color-input"
            />
          </div>
          <button class="apply-custom-btn" @click="applyCustomTheme">应用自定义主题</button>
        </div>
      </div>
      
      <div class="theme-actions">
        <button class="reset-btn" @click="resetTheme">重置为默认</button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted, onUnmounted } from 'vue'
import { useThemeStore, themeColors, type ThemeColorKey } from '@/stores/theme'

const themeStore = useThemeStore()
const showSelector = ref(false)

// 可用主题列表
const availableThemes = themeStore.getAvailableThemes()

// 自定义颜色
const customColors = reactive({
  primary: '#d97706',
  primaryLight: '#fef3e2',
  primaryDark: '#92400e'
})

// 选择预设主题
const selectTheme = (themeKey: ThemeColorKey) => {
  themeStore.setTheme(themeKey)
  showSelector.value = false
}

// 更新自定义主题颜色
const updateCustomTheme = () => {
  // 自动生成辅助色
  const secondary = adjustBrightness(customColors.primary, 20)
  const accent = adjustBrightness(customColors.primary, 40)
  
  customColors.primaryLight = lightenColor(customColors.primary, 90)
  customColors.primaryDark = darkenColor(customColors.primary, 30)
}

// 应用自定义主题
const applyCustomTheme = () => {
  const secondary = adjustBrightness(customColors.primary, 20)
  const accent = adjustBrightness(customColors.primary, 40)
  
  themeStore.setCustomTheme({
    name: '自定义主题',
    primary: customColors.primary,
    primaryLight: customColors.primaryLight,
    primaryDark: customColors.primaryDark,
    secondary,
    accent
  })
  showSelector.value = false
}

// 重置主题
const resetTheme = () => {
  themeStore.resetTheme()
  showSelector.value = false
}

// 颜色处理工具函数
const hexToRgb = (hex: string) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

const rgbToHex = (r: number, g: number, b: number) => {
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

const adjustBrightness = (hex: string, percent: number) => {
  const rgb = hexToRgb(hex)
  if (!rgb) return hex
  
  const factor = percent / 100
  const r = Math.min(255, Math.max(0, Math.round(rgb.r + (255 - rgb.r) * factor)))
  const g = Math.min(255, Math.max(0, Math.round(rgb.g + (255 - rgb.g) * factor)))
  const b = Math.min(255, Math.max(0, Math.round(rgb.b + (255 - rgb.b) * factor)))
  
  return rgbToHex(r, g, b)
}

const lightenColor = (hex: string, percent: number) => {
  return adjustBrightness(hex, percent)
}

const darkenColor = (hex: string, percent: number) => {
  const rgb = hexToRgb(hex)
  if (!rgb) return hex
  
  const factor = percent / 100
  const r = Math.max(0, Math.round(rgb.r * (1 - factor)))
  const g = Math.max(0, Math.round(rgb.g * (1 - factor)))
  const b = Math.max(0, Math.round(rgb.b * (1 - factor)))
  
  return rgbToHex(r, g, b)
}

// 点击外部关闭选择器
const handleClickOutside = (event: Event) => {
  const target = event.target as HTMLElement
  if (!target.closest('.theme-selector')) {
    showSelector.value = false
  }
}

onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  // 初始化自定义颜色
  customColors.primary = themeStore.activeTheme.primary
  customColors.primaryLight = themeStore.activeTheme.primaryLight
  customColors.primaryDark = themeStore.activeTheme.primaryDark
})

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
})
</script>

<style scoped>
.theme-selector {
  position: relative;
  display: inline-block;
}

.theme-selector-trigger {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  background: var(--color-surface);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  color: var(--color-text-primary);
}

.theme-selector-trigger:hover {
  border-color: var(--theme-primary);
}

.current-theme-indicator {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid #fff;
  box-shadow: 0 0 0 1px var(--color-border);
}

.arrow {
  font-size: 10px;
  transition: transform 0.2s ease;
}

.arrow-up {
  transform: rotate(180deg);
}

.theme-selector-panel {
  position: absolute;
  top: 100%;
  right: 0;
  width: 320px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 16px;
  z-index: 1000;
  margin-top: 4px;
}

.theme-section {
  margin-bottom: 20px;
}

.theme-section:last-child {
  margin-bottom: 0;
}

.theme-section h4 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.theme-option {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 12px 8px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.theme-option:hover {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-light);
}

.theme-option.active {
  border-color: var(--theme-primary);
  background-color: var(--theme-primary-light);
}

.theme-preview {
  display: flex;
  gap: 4px;
  margin-bottom: 8px;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(255, 255, 255, 0.8);
}

.theme-name {
  font-size: 12px;
  color: var(--color-text-secondary);
  text-align: center;
}

.custom-theme-editor {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.color-input-group {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.color-input-group label {
  font-size: 13px;
  color: var(--color-text-secondary);
}

.color-input {
  width: 40px;
  height: 30px;
  border: 1px solid var(--color-border);
  border-radius: 4px;
  cursor: pointer;
}

.apply-custom-btn {
  padding: 8px 16px;
  background: var(--theme-primary);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.apply-custom-btn:hover {
  background: var(--theme-primary-dark);
}

.theme-actions {
  border-top: 1px solid var(--color-border);
  padding-top: 12px;
}

.reset-btn {
  width: 100%;
  padding: 8px;
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  border-radius: 4px;
  font-size: 13px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}
</style>
