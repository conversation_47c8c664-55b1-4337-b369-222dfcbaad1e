import { defineStore } from 'pinia'
import { ref, computed } from 'vue'

// 预定义主题色方案
export const themeColors = {
  orange: {
    name: '橙色主题',
    primary: '#d97706',
    primaryLight: '#fef3e2',
    primaryDark: '#92400e',
    secondary: '#f59e0b',
    accent: '#fbbf24'
  },
  blue: {
    name: '蓝色主题',
    primary: '#2563eb',
    primaryLight: '#dbeafe',
    primaryDark: '#1d4ed8',
    secondary: '#3b82f6',
    accent: '#60a5fa'
  },
  green: {
    name: '绿色主题',
    primary: '#059669',
    primaryLight: '#d1fae5',
    primaryDark: '#047857',
    secondary: '#10b981',
    accent: '#34d399'
  },
  purple: {
    name: '紫色主题',
    primary: '#7c3aed',
    primaryLight: '#ede9fe',
    primaryDark: '#5b21b6',
    secondary: '#8b5cf6',
    accent: '#a78bfa'
  },
  red: {
    name: '红色主题',
    primary: '#dc2626',
    primaryLight: '#fee2e2',
    primaryDark: '#b91c1c',
    secondary: '#ef4444',
    accent: '#f87171'
  },
  teal: {
    name: '青色主题',
    primary: '#0891b2',
    primaryLight: '#cffafe',
    primaryDark: '#0e7490',
    secondary: '#06b6d4',
    accent: '#22d3ee'
  }
}

export type ThemeColorKey = keyof typeof themeColors

export const useThemeStore = defineStore('theme', () => {
  // 当前主题色
  const currentTheme = ref<ThemeColorKey>('orange')
  
  // 自定义主题色
  const customTheme = ref({
    name: '自定义主题',
    primary: '#d97706',
    primaryLight: '#fef3e2',
    primaryDark: '#92400e',
    secondary: '#f59e0b',
    accent: '#fbbf24'
  })

  // 是否使用自定义主题
  const useCustomTheme = ref(false)

  // 获取当前激活的主题
  const activeTheme = computed(() => {
    return useCustomTheme.value ? customTheme.value : themeColors[currentTheme.value]
  })

  // 应用主题到CSS变量
  const applyTheme = (theme: typeof customTheme.value) => {
    const root = document.documentElement
    root.style.setProperty('--theme-primary', theme.primary)
    root.style.setProperty('--theme-primary-light', theme.primaryLight)
    root.style.setProperty('--theme-primary-dark', theme.primaryDark)
    root.style.setProperty('--theme-secondary', theme.secondary)
    root.style.setProperty('--theme-accent', theme.accent)
  }

  // 切换预定义主题
  const setTheme = (themeKey: ThemeColorKey) => {
    currentTheme.value = themeKey
    useCustomTheme.value = false
    applyTheme(themeColors[themeKey])
    saveThemeToStorage()
  }

  // 设置自定义主题
  const setCustomTheme = (theme: Partial<typeof customTheme.value>) => {
    customTheme.value = { ...customTheme.value, ...theme }
    useCustomTheme.value = true
    applyTheme(customTheme.value)
    saveThemeToStorage()
  }

  // 保存主题到本地存储
  const saveThemeToStorage = () => {
    const themeData = {
      currentTheme: currentTheme.value,
      customTheme: customTheme.value,
      useCustomTheme: useCustomTheme.value
    }
    localStorage.setItem('user-center-theme', JSON.stringify(themeData))
  }

  // 从本地存储加载主题
  const loadThemeFromStorage = () => {
    try {
      const saved = localStorage.getItem('user-center-theme')
      if (saved) {
        const themeData = JSON.parse(saved)
        currentTheme.value = themeData.currentTheme || 'orange'
        customTheme.value = themeData.customTheme || customTheme.value
        useCustomTheme.value = themeData.useCustomTheme || false
        
        applyTheme(useCustomTheme.value ? customTheme.value : themeColors[currentTheme.value])
      } else {
        // 默认应用橙色主题
        applyTheme(themeColors.orange)
      }
    } catch (error) {
      console.error('Failed to load theme from storage:', error)
      applyTheme(themeColors.orange)
    }
  }

  // 重置为默认主题
  const resetTheme = () => {
    setTheme('orange')
  }

  // 获取所有可用主题
  const getAvailableThemes = () => {
    return Object.entries(themeColors).map(([key, theme]) => ({
      key: key as ThemeColorKey,
      ...theme
    }))
  }

  return {
    currentTheme,
    customTheme,
    useCustomTheme,
    activeTheme,
    setTheme,
    setCustomTheme,
    resetTheme,
    loadThemeFromStorage,
    getAvailableThemes,
    applyTheme
  }
})
