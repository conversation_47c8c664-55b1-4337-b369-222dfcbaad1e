import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import MyInfo from '@/views/MyInfo.vue'
import BillManage from '@/views/BillManage.vue'
import ApiKeyManage from '@/views/ApiKeyManage.vue'
import UsageInfo from '@/views/UsageInfo.vue'
import Recharge from '@/views/Recharge.vue'

const routes: Array<RouteRecordRaw> = [
  {
    path: '/',
    redirect: '/my-info'
  },
  {
    path: '/my-info',
    name: 'MyInfo',
    component: MyInfo
  },
  {
    path: '/bill-manage',
    name: 'BillManage',
    component: BillManage
  },
  {
    path: '/api-key-manage',
    name: 'ApiKeyManage',
    component: ApiKeyManage
  },
  {
    path: '/usage-info',
    name: 'UsageInfo',
    component: UsageInfo
  },
  {
    path: '/recharge',
    name: 'Recharge',
    component: Recharge
  }
]

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes
})

export default router