<script setup lang="ts">
import { RouterLink, RouterView } from 'vue-router'
</script>

<template>
  <div class="app-container">
    <!-- 顶部导航栏 -->
    <header class="top-header">
      <div class="logo">
        <span class="logo-text">deepseek</span>
        <span class="beta-tag">开发平台</span>
      </div>
    </header>

    <!-- 主体内容区域 -->
    <div class="main-layout">
      <!-- 左侧边栏 -->
      <aside class="sidebar">
        <nav class="sidebar-nav">
          <RouterLink to="/usage-info" class="nav-item">
            <i class="icon">📊</i>
            <span>用量信息</span>
          </RouterLink>
          <RouterLink to="/api-key-manage" class="nav-item">
            <i class="icon">🔑</i>
            <span>API keys</span>
          </RouterLink>
          <RouterLink to="/recharge" class="nav-item">
            <i class="icon">💰</i>
            <span>充值</span>
          </RouterLink>
          <RouterLink to="/bill-manage" class="nav-item">
            <i class="icon">📋</i>
            <span>账单</span>
          </RouterLink>
          <RouterLink to="/my-info" class="nav-item">
            <i class="icon">👤</i>
            <span>个人信息</span>
          </RouterLink>
        </nav>

        <div class="sidebar-footer">
          <div class="help-link">
            <span>网页版免费体验</span>
            <i class="arrow">↗</i>
          </div>
        </div>
      </aside>

      <!-- 右侧主内容区域 -->
      <main class="main-content">
        <RouterView />
      </main>
    </div>
  </div>
</template>

<style scoped>
.app-container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f5f5f5;
}

/* 顶部导航栏 */
.top-header {
  background: #ffffff;
  border-bottom: 1px solid #e5e5e5;
  padding: 0 24px;
  height: 64px;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
}

.logo-text {
  font-size: 20px;
  font-weight: 600;
  color: #1a1a1a;
}

.beta-tag {
  background: #1a1a1a;
  color: white;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

/* 主体布局 */
.main-layout {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 左侧边栏 */
.sidebar {
  width: 240px;
  background: #ffffff;
  border-right: 1px solid #e5e5e5;
  display: flex;
  flex-direction: column;
  padding: 24px 0;
}

.sidebar-nav {
  flex: 1;
  padding: 0 16px;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  margin-bottom: 4px;
  border-radius: 8px;
  text-decoration: none;
  color: #666666;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s ease;
}

.nav-item:hover {
  background-color: #f5f5f5;
  color: #1a1a1a;
}

.nav-item.router-link-active,
.nav-item.active {
  background-color: #fef3e2;
  color: #d97706;
}

.nav-item .icon {
  font-size: 16px;
  width: 20px;
  text-align: center;
}

/* 侧边栏底部 */
.sidebar-footer {
  padding: 0 32px;
  margin-top: auto;
}

.help-link {
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #666666;
  font-size: 14px;
  cursor: pointer;
  padding: 8px 0;
}

.help-link:hover {
  color: #1a1a1a;
}

.arrow {
  font-size: 12px;
}

/* 主内容区域 */
.main-content {
  flex: 1;
  background: #ffffff;
  overflow-y: auto;
  padding: 32px;
}
</style>
