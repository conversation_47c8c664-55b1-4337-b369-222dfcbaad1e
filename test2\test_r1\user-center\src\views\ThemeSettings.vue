<template>
  <div class="theme-settings">
    <div class="page-header">
      <h1 class="page-title">主题设置</h1>
      <p class="page-description">自定义您的界面主题色彩，让体验更加个性化</p>
    </div>

    <!-- 预设主题 -->
    <div class="settings-section">
      <h2 class="section-title">预设主题</h2>
      <div class="theme-grid">
        <div 
          v-for="theme in availableThemes" 
          :key="theme.key"
          class="theme-card"
          :class="{ active: !themeStore.useCustomTheme && themeStore.currentTheme === theme.key }"
          @click="selectTheme(theme.key)"
        >
          <div class="theme-preview">
            <div class="preview-header" :style="{ backgroundColor: theme.primary }">
              <div class="preview-logo">
                <div class="logo-dot" :style="{ backgroundColor: theme.primaryLight }"></div>
                <span>deepseek</span>
              </div>
            </div>
            <div class="preview-content">
              <div class="preview-sidebar">
                <div class="sidebar-item" :style="{ backgroundColor: theme.primaryLight, color: theme.primary }">
                  <div class="item-icon" :style="{ backgroundColor: theme.primary }"></div>
                </div>
                <div class="sidebar-item"></div>
                <div class="sidebar-item"></div>
              </div>
              <div class="preview-main">
                <div class="main-card">
                  <div class="card-button" :style="{ backgroundColor: theme.primary }"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="theme-info">
            <h3 class="theme-name">{{ theme.name }}</h3>
            <div class="color-palette">
              <div class="color-dot" :style="{ backgroundColor: theme.primary }" :title="theme.primary"></div>
              <div class="color-dot" :style="{ backgroundColor: theme.secondary }" :title="theme.secondary"></div>
              <div class="color-dot" :style="{ backgroundColor: theme.accent }" :title="theme.accent"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 自定义主题 -->
    <div class="settings-section">
      <h2 class="section-title">自定义主题</h2>
      <div class="custom-theme-panel">
        <div class="color-editor">
          <div class="color-group">
            <label class="color-label">主色调</label>
            <div class="color-input-wrapper">
              <input 
                type="color" 
                v-model="customColors.primary" 
                @input="updateCustomTheme"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customColors.primary" 
                @input="updateCustomTheme"
                class="color-text"
                placeholder="#d97706"
              />
            </div>
          </div>
          
          <div class="color-group">
            <label class="color-label">浅色调</label>
            <div class="color-input-wrapper">
              <input 
                type="color" 
                v-model="customColors.primaryLight" 
                @input="updateCustomTheme"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customColors.primaryLight" 
                @input="updateCustomTheme"
                class="color-text"
                placeholder="#fef3e2"
              />
            </div>
          </div>
          
          <div class="color-group">
            <label class="color-label">深色调</label>
            <div class="color-input-wrapper">
              <input 
                type="color" 
                v-model="customColors.primaryDark" 
                @input="updateCustomTheme"
                class="color-picker"
              />
              <input 
                type="text" 
                v-model="customColors.primaryDark" 
                @input="updateCustomTheme"
                class="color-text"
                placeholder="#92400e"
              />
            </div>
          </div>
        </div>
        
        <div class="custom-preview">
          <div class="preview-container">
            <div class="mini-preview">
              <div class="mini-header" :style="{ backgroundColor: customColors.primary }"></div>
              <div class="mini-content">
                <div class="mini-sidebar">
                  <div class="mini-item active" :style="{ backgroundColor: customColors.primaryLight, borderColor: customColors.primary }"></div>
                  <div class="mini-item"></div>
                  <div class="mini-item"></div>
                </div>
                <div class="mini-main">
                  <div class="mini-button" :style="{ backgroundColor: customColors.primary }"></div>
                </div>
              </div>
            </div>
          </div>
          <div class="custom-actions">
            <button class="apply-btn" @click="applyCustomTheme" :disabled="!isCustomThemeValid">
              应用自定义主题
            </button>
            <button class="reset-btn" @click="resetCustomColors">
              重置
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 主题管理 -->
    <div class="settings-section">
      <h2 class="section-title">主题管理</h2>
      <div class="theme-actions">
        <button class="action-btn" @click="exportTheme">
          <i class="icon">📤</i>
          导出当前主题
        </button>
        <button class="action-btn" @click="importTheme">
          <i class="icon">📥</i>
          导入主题
        </button>
        <button class="action-btn danger" @click="resetAllThemes">
          <i class="icon">🔄</i>
          恢复默认设置
        </button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useThemeStore, type ThemeColorKey } from '@/stores/theme'

const themeStore = useThemeStore()

// 可用主题列表
const availableThemes = themeStore.getAvailableThemes()

// 自定义颜色
const customColors = reactive({
  primary: '#d97706',
  primaryLight: '#fef3e2',
  primaryDark: '#92400e'
})

// 验证自定义主题是否有效
const isCustomThemeValid = computed(() => {
  const hexRegex = /^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/
  return hexRegex.test(customColors.primary) && 
         hexRegex.test(customColors.primaryLight) && 
         hexRegex.test(customColors.primaryDark)
})

// 选择预设主题
const selectTheme = (themeKey: ThemeColorKey) => {
  themeStore.setTheme(themeKey)
}

// 更新自定义主题颜色
const updateCustomTheme = () => {
  // 实时预览效果可以在这里添加
}

// 应用自定义主题
const applyCustomTheme = () => {
  if (!isCustomThemeValid.value) return
  
  // 自动生成辅助色
  const secondary = adjustBrightness(customColors.primary, 20)
  const accent = adjustBrightness(customColors.primary, 40)
  
  themeStore.setCustomTheme({
    name: '自定义主题',
    primary: customColors.primary,
    primaryLight: customColors.primaryLight,
    primaryDark: customColors.primaryDark,
    secondary,
    accent
  })
}

// 重置自定义颜色
const resetCustomColors = () => {
  customColors.primary = '#d97706'
  customColors.primaryLight = '#fef3e2'
  customColors.primaryDark = '#92400e'
}

// 导出主题
const exportTheme = () => {
  const themeData = {
    currentTheme: themeStore.currentTheme,
    customTheme: themeStore.customTheme,
    useCustomTheme: themeStore.useCustomTheme
  }
  
  const dataStr = JSON.stringify(themeData, null, 2)
  const dataBlob = new Blob([dataStr], { type: 'application/json' })
  const url = URL.createObjectURL(dataBlob)
  
  const link = document.createElement('a')
  link.href = url
  link.download = 'theme-config.json'
  link.click()
  
  URL.revokeObjectURL(url)
}

// 导入主题
const importTheme = () => {
  const input = document.createElement('input')
  input.type = 'file'
  input.accept = '.json'
  input.onchange = (e) => {
    const file = (e.target as HTMLInputElement).files?.[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        try {
          const themeData = JSON.parse(e.target?.result as string)
          if (themeData.useCustomTheme) {
            themeStore.setCustomTheme(themeData.customTheme)
          } else {
            themeStore.setTheme(themeData.currentTheme)
          }
        } catch (error) {
          alert('主题文件格式错误')
        }
      }
      reader.readAsText(file)
    }
  }
  input.click()
}

// 重置所有主题设置
const resetAllThemes = () => {
  if (confirm('确定要恢复默认主题设置吗？这将清除所有自定义配置。')) {
    themeStore.resetTheme()
    resetCustomColors()
  }
}

// 颜色处理工具函数
const adjustBrightness = (hex: string, percent: number) => {
  const num = parseInt(hex.replace("#", ""), 16)
  const amt = Math.round(2.55 * percent)
  const R = (num >> 16) + amt
  const G = (num >> 8 & 0x00FF) + amt
  const B = (num & 0x0000FF) + amt
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1)
}

onMounted(() => {
  // 初始化自定义颜色
  customColors.primary = themeStore.activeTheme.primary
  customColors.primaryLight = themeStore.activeTheme.primaryLight
  customColors.primaryDark = themeStore.activeTheme.primaryDark
})
</script>

<style scoped>
.theme-settings {
  max-width: 1000px;
  margin: 0 auto;
  padding: 0;
}

.page-header {
  margin-bottom: 32px;
}

.page-title {
  font-size: 28px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
}

.page-description {
  font-size: 16px;
  color: var(--color-text-secondary);
  margin: 0;
}

.settings-section {
  margin-bottom: 48px;
}

.section-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 20px 0;
}

.theme-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.theme-card {
  border: 2px solid var(--color-border);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--color-surface);
}

.theme-card:hover {
  border-color: var(--theme-primary);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.theme-card.active {
  border-color: var(--theme-primary);
  box-shadow: 0 0 0 3px var(--theme-primary-light);
}

.theme-preview {
  height: 120px;
  position: relative;
  overflow: hidden;
}

.preview-header {
  height: 24px;
  display: flex;
  align-items: center;
  padding: 0 12px;
}

.preview-logo {
  display: flex;
  align-items: center;
  gap: 6px;
  color: white;
  font-size: 10px;
  font-weight: 600;
}

.logo-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.preview-content {
  display: flex;
  height: 96px;
}

.preview-sidebar {
  width: 60px;
  background: #f8f9fa;
  padding: 8px 6px;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.sidebar-item {
  height: 16px;
  border-radius: 4px;
  background: #e9ecef;
  display: flex;
  align-items: center;
  padding: 0 4px;
}

.item-icon {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: #6c757d;
}

.preview-main {
  flex: 1;
  background: white;
  padding: 8px;
}

.main-card {
  background: #f8f9fa;
  border-radius: 4px;
  height: 100%;
  padding: 6px;
  display: flex;
  align-items: flex-end;
}

.card-button {
  width: 40px;
  height: 12px;
  border-radius: 2px;
}

.theme-info {
  padding: 16px;
}

.theme-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
  margin: 0 0 8px 0;
}

.color-palette {
  display: flex;
  gap: 6px;
}

.color-dot {
  width: 16px;
  height: 16px;
  border-radius: 50%;
  border: 2px solid white;
  box-shadow: 0 0 0 1px var(--color-border);
  cursor: help;
}

.custom-theme-panel {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 32px;
  padding: 24px;
  background: var(--color-surface);
  border-radius: 12px;
  border: 1px solid var(--color-border);
}

.color-editor {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.color-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.color-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--color-text-primary);
}

.color-input-wrapper {
  display: flex;
  gap: 12px;
  align-items: center;
}

.color-picker {
  width: 50px;
  height: 40px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
}

.color-text {
  flex: 1;
  padding: 8px 12px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  font-family: monospace;
  font-size: 14px;
}

.custom-preview {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.preview-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.mini-preview {
  width: 200px;
  height: 120px;
  border: 1px solid var(--color-border);
  border-radius: 6px;
  overflow: hidden;
  background: white;
}

.mini-header {
  height: 20px;
}

.mini-content {
  display: flex;
  height: 100px;
}

.mini-sidebar {
  width: 50px;
  background: #f8f9fa;
  padding: 6px 4px;
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.mini-item {
  height: 12px;
  border-radius: 2px;
  background: #e9ecef;
}

.mini-item.active {
  border: 1px solid;
}

.mini-main {
  flex: 1;
  padding: 6px;
  display: flex;
  align-items: flex-end;
}

.mini-button {
  width: 30px;
  height: 8px;
  border-radius: 2px;
}

.custom-actions {
  display: flex;
  gap: 12px;
}

.apply-btn {
  flex: 1;
  padding: 10px 16px;
  background: var(--theme-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.apply-btn:hover:not(:disabled) {
  background: var(--theme-primary-dark);
}

.apply-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.reset-btn {
  padding: 10px 16px;
  background: transparent;
  color: var(--color-text-secondary);
  border: 1px solid var(--color-border);
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.reset-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.theme-actions {
  display: flex;
  gap: 16px;
  flex-wrap: wrap;
}

.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--color-surface);
  border: 1px solid var(--color-border);
  border-radius: 8px;
  color: var(--color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
}

.action-btn:hover {
  border-color: var(--theme-primary);
  color: var(--theme-primary);
}

.action-btn.danger:hover {
  border-color: var(--color-danger);
  color: var(--color-danger);
}

.icon {
  font-size: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-theme-panel {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .theme-grid {
    grid-template-columns: 1fr;
  }
  
  .theme-actions {
    flex-direction: column;
  }
  
  .action-btn {
    justify-content: center;
  }
}
</style>
