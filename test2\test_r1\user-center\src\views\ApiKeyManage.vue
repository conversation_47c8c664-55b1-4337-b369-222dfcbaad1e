<script setup lang="ts">
import { ref } from 'vue'
import { useApiKeyStore } from '@/stores/apiKey'

const apiKeyStore = useApiKeyStore()
const newKeyName = ref('')
const dialogVisible = ref(false)
const editDialogVisible = ref(false)
const editingKey = ref({ id: '', name: '' })

const createNewKey = () => {
  if (newKeyName.value.trim()) {
    apiKeyStore.createKey(newKeyName.value.trim())
    newKeyName.value = ''
    dialogVisible.value = false
  }
}

const editKeyName = (key: any) => {
  editingKey.value = { id: key.id, name: key.name }
  editDialogVisible.value = true
}

const updateKeyName = () => {
  if (editingKey.value.name.trim()) {
    const key = apiKeyStore.apiKeys.find(k => k.id === editingKey.value.id)
    if (key) key.name = editingKey.value.name.trim()
    editDialogVisible.value = false
  }
}
</script>

<template>
  <div class="api-key-manage">
    <div class="header">
      <h2>API Key 管理</h2>
      <el-button type="primary" @click="dialogVisible = true">创建 API Key</el-button>
    </div>
    
    <div class="key-list">
      <el-table :data="apiKeyStore.apiKeys" style="width: 100%" border>
        <el-table-column prop="name" label="名称" width="180" />
        <el-table-column label="Key">
          <template #default="scope">
            <div class="key-display">
              <span v-if="apiKeyStore.showKey[scope.row.id]">{{ scope.row.key }}</span>
              <span v-else>{{ scope.row.key.replace(/./g, '*') }}</span>
              <el-button 
                :icon="apiKeyStore.showKey[scope.row.id] ? 'el-icon-view' : 'el-icon-lock'" 
                circle 
                size="small"
                @click="apiKeyStore.toggleKeyVisibility(scope.row.id)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column prop="created" label="创建时间" width="120" />
        <el-table-column prop="lastUsed" label="最后使用" width="120" />
        <el-table-column label="操作" width="200">
          <template #default="scope">
            <el-button 
              type="primary" 
              size="small" 
              @click="editKeyName(scope.row)"
            >
              编辑
            </el-button>
            <el-button 
              type="success" 
              size="small" 
              @click="apiKeyStore.copyToClipboard(scope.row.key)"
            >
              复制
            </el-button>
            <el-button 
              type="danger" 
              size="small" 
              @click="apiKeyStore.deleteKey(scope.row.id)"
            >
              删除
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    
    <!-- 创建Key对话框 -->
    <el-dialog v-model="dialogVisible" title="创建新的API Key" width="30%">
      <el-form>
        <el-form-item label="Key名称">
          <el-input v-model="newKeyName" placeholder="请输入Key名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="createNewKey">创建</el-button>
      </template>
    </el-dialog>
    
    <!-- 编辑Key对话框 -->
    <el-dialog v-model="editDialogVisible" title="编辑Key名称" width="30%">
      <el-form>
        <el-form-item label="Key名称">
          <el-input v-model="editingKey.name" placeholder="请输入新的Key名称" />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="editDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="updateKeyName">保存</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<style scoped>
.api-key-manage {
  padding: 20px;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
}

.key-list {
  margin-top: 20px;
}

.key-display {
  display: flex;
  align-items: center;
  gap: 10px;
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 767px) {
  .api-key-manage {
    padding: 16px;
  }

  .header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }

  .header h2 {
    font-size: 20px;
    margin: 0;
  }

  .key-list {
    margin-top: 16px;
    overflow-x: auto;
    -webkit-overflow-scrolling: touch;
  }

  .el-table {
    min-width: 700px;
    font-size: 12px;
  }

  .el-table .el-table__cell {
    padding: 6px 4px;
  }

  .key-display {
    gap: 6px;
  }

  .key-display span {
    font-size: 11px;
    word-break: break-all;
  }

  .el-button--small {
    padding: 4px 6px;
    font-size: 10px;
  }

  /* 对话框优化 */
  .el-dialog {
    width: 95% !important;
    margin: 0 2.5%;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .api-key-manage {
    padding: 20px;
  }

  .header h2 {
    font-size: 22px;
  }

  .el-table {
    font-size: 14px;
  }

  .el-dialog {
    width: 80% !important;
  }
}

/* 表格操作按钮优化 */
@media (max-width: 1023px) {
  .key-display {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .key-display .el-button {
    align-self: flex-end;
  }
}
</style>
