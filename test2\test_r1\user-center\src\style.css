@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

* {
  box-sizing: border-box;
}

:root {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light;
  color: #1a1a1a;
  background-color: #f5f5f5;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  color: #1a1a1a;
  background-color: #f5f5f5;
  overflow-x: hidden; /* 防止水平滚动 */
}

#app {
  width: 100%;
  height: 100vh;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

a {
  font-weight: 500;
  color: #646cff;
  text-decoration: inherit;
}
a:hover {
  color: #535bf2;
}

button {
  border-radius: 8px;
  border: 1px solid transparent;
  padding: 0.6em 1.2em;
  font-size: 1em;
  font-weight: 500;
  font-family: inherit;
  background-color: #1a1a1a;
  cursor: pointer;
  transition: border-color 0.25s;
}
button:hover {
  border-color: #646cff;
}
button:focus,
button:focus-visible {
  outline: 4px auto -webkit-focus-ring-color;
}

@media (prefers-color-scheme: light) {
  :root {
    color: #213547;
    background-color: #ffffff;
  }
  a:hover {
    color: #747bff;
  }
  button {
    background-color: #f9f9f9;
  }
}

.el-button {
  transition: all 0.3s;
}

.info-card, .recharge-card, .bill-list {
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.el-table {
  border-radius: 8px;
  overflow: hidden;
}

/* 响应式表格优化 */
@media (max-width: 767px) {
  .el-table {
    font-size: 14px;
  }

  .el-table .el-table__cell {
    padding: 8px 4px;
  }

  .el-button {
    padding: 6px 12px;
    font-size: 12px;
  }

  .el-button--small {
    padding: 4px 8px;
    font-size: 11px;
  }

  .el-input__inner {
    font-size: 16px; /* 防止iOS缩放 */
  }

  .el-dialog {
    width: 90% !important;
    margin: 0 5%;
  }

  .el-tabs__item {
    padding: 0 12px;
    font-size: 14px;
  }
}

/* 平板优化 */
@media (min-width: 768px) and (max-width: 1023px) {
  .el-table {
    font-size: 15px;
  }

  .el-dialog {
    width: 70% !important;
  }
}

/* 触摸设备优化 */
@media (hover: none) and (pointer: coarse) {
  .el-button {
    min-height: 44px;
    min-width: 44px;
  }

  .el-input__inner {
    min-height: 44px;
  }

  .el-radio-button__inner {
    min-height: 44px;
    display: flex;
    align-items: center;
  }
}

/* 高分辨率屏幕优化 */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .info-card, .recharge-card, .bill-list {
    box-shadow: 0 1px 6px 0 rgba(0, 0, 0, 0.1);
  }
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
  /* 暂时保持浅色主题，可以后续添加深色模式 */
}

/* 减少动画的用户偏好 */
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}
