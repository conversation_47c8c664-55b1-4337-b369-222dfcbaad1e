<template>
  <div class="about-us-overlay" v-if="visible" @click="handleOverlayClick">
    <div class="about-dialog" @click.stop>
      <div class="dialog-header">
        <h3>关于我们</h3>
        <button class="close-btn" @click="$emit('close')">×</button>
      </div>
      
      <div class="dialog-content">
        <div class="about-content">
          <h4>关于我们</h4>
          <p>这里是关于我们的内容占位...</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
defineProps<{
  visible: boolean
}>()

const emit = defineEmits<{
  close: []
}>()

const handleOverlayClick = () => {
  emit('close')
}
</script>

<style scoped>
.about-us-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  padding: 20px;
}

.about-dialog {
  background: var(--color-surface);
  border-radius: 12px;
  width: 100%;
  max-width: 500px;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
}

.dialog-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px 16px;
  border-bottom: 1px solid var(--color-border);
}

.dialog-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  color: var(--color-text-secondary);
  cursor: pointer;
  padding: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.close-btn:hover {
  background-color: var(--color-border);
  color: var(--color-text-primary);
}

.dialog-content {
  padding: 24px;
}

.about-content h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--color-text-primary);
}

.about-content p {
  margin: 0;
  color: var(--color-text-secondary);
  line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .about-us-overlay {
    padding: 16px;
  }
  
  .about-dialog {
    max-width: none;
  }
  
  .dialog-header {
    padding: 16px 20px 12px;
  }
  
  .dialog-content {
    padding: 20px;
  }
}
</style>
